#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Demo script for enhanced asset import functionality.

This script demonstrates the new features of import_asset_by_name function:
1. Automatic file type detection (FBX/OBJ)
2. Automatic axis alignment based on asset and scene up axis
3. Support for both FBX and OBJ formats

Usage:
    # In Maya Python console or script editor:
    import cgame_avatar_factory.hair_studio.maya_api.demo_asset_import as demo
    demo.demo_import_assets()
"""

import logging
import os

# Import the enhanced modules
import cgame_avatar_factory.hair_studio.maya_api.utils as maya_utils
import cgame_avatar_factory.hair_studio.maya_api.load_maya_asset as load_maya_asset


def demo_file_type_detection():
    """Demonstrate file type detection functionality."""
    print("\n=== File Type Detection Demo ===")
    
    # Test different file types
    test_files = [
        "test_hair.fbx",
        "test_hair.obj", 
        "test_hair.ma",
        "test_hair.mb",
        "nonexistent.fbx"
    ]
    
    for file_path in test_files:
        file_type = maya_utils.get_asset_file_type(file_path)
        print(f"File: {file_path} -> Type: {file_type}")


def demo_axis_detection():
    """Demonstrate axis detection functionality."""
    print("\n=== Axis Detection Demo ===")
    
    # Test axis detection for different file types
    test_files = [
        ("test_hair.fbx", "FBX"),
        ("test_hair.obj", "OBJ")
    ]
    
    for file_path, file_type in test_files:
        if file_type == "FBX":
            axis = maya_utils.get_fbx_up_axis(file_path)
        else:
            axis = maya_utils.get_obj_up_axis(file_path)
        print(f"File: {file_path} ({file_type}) -> Up Axis: {axis}")
    
    # Show scene axis
    scene_axis = maya_utils.get_scene_up_axis()
    print(f"Current Maya Scene Up Axis: {scene_axis}")


def demo_import_fbx_asset(fbx_path, group_name="imported_fbx_group"):
    """
    Demonstrate importing an FBX asset.
    
    Args:
        fbx_path (str): Path to the FBX file
        group_name (str): Name for the import group
    """
    print(f"\n=== Importing FBX Asset: {fbx_path} ===")
    
    if not os.path.exists(fbx_path):
        print(f"Warning: File does not exist: {fbx_path}")
        return None
    
    # Import the asset
    result = load_maya_asset.import_asset_by_name(fbx_path, group_name)
    
    if result:
        print(f"Successfully imported FBX asset: {result}")
        print(f"Asset grouped under: {group_name}")
        
        # Show asset information
        asset_type = maya_utils.get_asset_file_type(fbx_path)
        asset_axis = maya_utils.get_asset_up_axis(fbx_path)
        scene_axis = maya_utils.get_scene_up_axis()
        
        print(f"Asset Type: {asset_type}")
        print(f"Asset Up Axis: {asset_axis}")
        print(f"Scene Up Axis: {scene_axis}")
        
        if asset_axis and scene_axis and asset_axis.lower() != scene_axis.lower():
            print(f"Note: Asset was automatically aligned from {asset_axis}-up to {scene_axis}-up")
        else:
            print("Note: No axis alignment was needed")
    else:
        print("Failed to import FBX asset")
    
    return result


def demo_import_obj_asset(obj_path, group_name="imported_obj_group"):
    """
    Demonstrate importing an OBJ asset.
    
    Args:
        obj_path (str): Path to the OBJ file
        group_name (str): Name for the import group
    """
    print(f"\n=== Importing OBJ Asset: {obj_path} ===")
    
    if not os.path.exists(obj_path):
        print(f"Warning: File does not exist: {obj_path}")
        return None
    
    # Import the asset
    result = load_maya_asset.import_asset_by_name(obj_path, group_name)
    
    if result:
        print(f"Successfully imported OBJ asset: {result}")
        print(f"Asset grouped under: {group_name}")
        
        # Show asset information
        asset_type = maya_utils.get_asset_file_type(obj_path)
        asset_axis = maya_utils.get_asset_up_axis(obj_path)
        scene_axis = maya_utils.get_scene_up_axis()
        
        print(f"Asset Type: {asset_type}")
        print(f"Asset Up Axis: {asset_axis}")
        print(f"Scene Up Axis: {scene_axis}")
        
        if asset_axis and scene_axis and asset_axis.lower() != scene_axis.lower():
            print(f"Note: Asset was automatically aligned from {asset_axis}-up to {scene_axis}-up")
        else:
            print("Note: No axis alignment was needed")
    else:
        print("Failed to import OBJ asset")
    
    return result


def demo_import_assets():
    """
    Main demo function that shows all the new functionality.
    
    This function demonstrates:
    1. File type detection
    2. Axis detection
    3. Importing FBX and OBJ assets with automatic type detection
    """
    print("=== Enhanced Asset Import Demo ===")
    print("This demo shows the new features of import_asset_by_name function:")
    print("1. Automatic file type detection (FBX/OBJ)")
    print("2. Automatic axis alignment")
    print("3. Support for both FBX and OBJ formats")
    
    # Demo file type detection
    demo_file_type_detection()
    
    # Demo axis detection
    demo_axis_detection()
    
    print("\n=== Usage Examples ===")
    print("To import an asset, use:")
    print("import cgame_avatar_factory.hair_studio.maya_api.load_maya_asset as load_maya_asset")
    print("result = load_maya_asset.import_asset_by_name('/path/to/asset.fbx', 'my_group')")
    print("result = load_maya_asset.import_asset_by_name('/path/to/asset.obj', 'my_group')")
    
    print("\nThe function will automatically:")
    print("- Detect if the file is FBX or OBJ")
    print("- Use the appropriate Maya import type")
    print("- Align the asset axis to match the scene axis if needed")
    print("- Load required plugins (like FBX plugin)")
    
    print("\n=== Demo Complete ===")


if __name__ == "__main__":
    # Set up logging to see debug messages
    logging.basicConfig(level=logging.DEBUG)
    
    # Run the demo
    demo_import_assets()
