#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for asset import functionality.

This script tests the enhanced import_asset_by_name function that supports
both FBX and OBJ file formats with automatic type detection and axis alignment.
"""

import logging
import os
import sys
import tempfile
import unittest
from unittest.mock import Mock, patch, MagicMock

# Add the project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Import the modules we want to test
try:
    import cgame_avatar_factory.hair_studio.maya_api.utils as maya_utils
    import cgame_avatar_factory.hair_studio.maya_api.load_maya_asset as load_maya_asset
except ImportError as e:
    print(f"Import error: {e}")
    print(f"Python path: {sys.path}")
    raise


class TestAssetImport(unittest.TestCase):
    """Test cases for asset import functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.logger = logging.getLogger(__name__)
        
        # Create temporary test files
        self.temp_dir = tempfile.mkdtemp()
        self.test_fbx_path = os.path.join(self.temp_dir, "test_asset.fbx")
        self.test_obj_path = os.path.join(self.temp_dir, "test_asset.obj")
        self.test_invalid_path = os.path.join(self.temp_dir, "test_asset.txt")
        
        # Create empty test files
        for path in [self.test_fbx_path, self.test_obj_path, self.test_invalid_path]:
            with open(path, 'w') as f:
                f.write("# Test file")

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_get_asset_file_type_fbx(self):
        """Test FBX file type detection."""
        result = maya_utils.get_asset_file_type(self.test_fbx_path)
        self.assertEqual(result, "FBX")

    def test_get_asset_file_type_obj(self):
        """Test OBJ file type detection."""
        result = maya_utils.get_asset_file_type(self.test_obj_path)
        self.assertEqual(result, "OBJ")

    def test_get_asset_file_type_invalid(self):
        """Test invalid file type detection."""
        result = maya_utils.get_asset_file_type(self.test_invalid_path)
        self.assertIsNone(result)

    def test_get_asset_file_type_nonexistent(self):
        """Test nonexistent file handling."""
        result = maya_utils.get_asset_file_type("/nonexistent/file.fbx")
        self.assertIsNone(result)

    def test_get_obj_up_axis(self):
        """Test OBJ up axis detection."""
        result = maya_utils.get_obj_up_axis(self.test_obj_path)
        self.assertEqual(result, "Y")

    @patch('cgame_avatar_factory.hair_studio.maya_api.utils.is_maya_available')
    def test_get_scene_up_axis_maya_not_available(self, mock_maya_available):
        """Test scene up axis when Maya is not available."""
        mock_maya_available.return_value = False
        result = maya_utils.get_scene_up_axis()
        self.assertIsNone(result)

    @patch('cgame_avatar_factory.hair_studio.maya_api.utils.is_maya_available')
    @patch('cgame_avatar_factory.hair_studio.maya_api.utils.get_maya_cmds')
    def test_get_scene_up_axis_maya_available(self, mock_get_cmds, mock_maya_available):
        """Test scene up axis when Maya is available."""
        mock_maya_available.return_value = True
        mock_cmds = Mock()
        mock_cmds.upAxis.return_value = "y"
        mock_get_cmds.return_value = mock_cmds
        
        result = maya_utils.get_scene_up_axis()
        self.assertEqual(result, "Y")

    @patch('cgame_avatar_factory.hair_studio.maya_api.utils.get_fbx_up_axis')
    @patch('cgame_avatar_factory.hair_studio.maya_api.utils.get_asset_file_type')
    def test_get_asset_up_axis_fbx(self, mock_get_type, mock_get_fbx_axis):
        """Test asset up axis detection for FBX."""
        mock_get_type.return_value = "FBX"
        mock_get_fbx_axis.return_value = "Y"

        result = maya_utils.get_asset_up_axis(self.test_fbx_path)

        mock_get_type.assert_called_with(self.test_fbx_path)
        mock_get_fbx_axis.assert_called_with(self.test_fbx_path)
        self.assertEqual(result, "Y")

    def test_legacy_functions(self):
        """Test legacy function compatibility."""
        # These should not raise exceptions
        result = maya_utils.check_obj_yz_up()
        self.assertEqual(result, "Y")
        
        # These functions are placeholders
        maya_utils.check_fbx_zy_up()
        maya_utils.check_file_yz_up()


class TestAssetImportIntegration(unittest.TestCase):
    """Integration tests for asset import functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.test_fbx_path = os.path.join(self.temp_dir, "test_asset.fbx")
        self.test_obj_path = os.path.join(self.temp_dir, "test_asset.obj")
        
        # Create empty test files
        for path in [self.test_fbx_path, self.test_obj_path]:
            with open(path, 'w') as f:
                f.write("# Test file")

    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @patch('os.path.exists')
    def test_import_asset_by_name_nonexistent_file(self, mock_exists):
        """Test import with nonexistent file."""
        mock_exists.return_value = False
        
        result = load_maya_asset.import_asset_by_name("/nonexistent/file.fbx", "test_group")
        self.assertIsNone(result)

    @patch('cgame_avatar_factory.hair_studio.maya_api.utils.get_asset_file_type')
    def test_import_asset_by_name_unsupported_type(self, mock_get_type):
        """Test import with unsupported file type."""
        mock_get_type.return_value = None
        
        result = load_maya_asset.import_asset_by_name(self.test_fbx_path, "test_group")
        self.assertIsNone(result)


if __name__ == '__main__':
    # Set up logging
    logging.basicConfig(level=logging.DEBUG)
    
    # Run tests
    unittest.main()
